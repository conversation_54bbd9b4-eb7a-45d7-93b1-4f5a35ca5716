#!/usr/bin/env python3
"""
Script to update role permissions to include action_items permissions
"""
import os
import sys
import json
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def update_role_permissions():
    # Try different database configurations
    database_urls = [
        os.getenv('DATABASE_URL'),
        'postgresql://aggie_user:vV@YPA1C&Ki8U3@localhost:5432/aggie_prod',
        'postgresql://postgres:postgres@localhost:5432/aggie_dev',
        'postgresql://postgres:postgres@localhost:5432/aggie_prod'
    ]

    # Filter out None values
    database_urls = [url for url in database_urls if url]
    
    for database_url in database_urls:
        try:
            print(f"Trying database connection: {database_url}")
            # Create engine and session
            engine = create_engine(database_url)
            Session = sessionmaker(bind=engine)
            session = Session()

            # Test connection
            session.execute(text("SELECT 1"))
            print(f"✓ Connected to database successfully!")
            print("Updating role permissions...")
            break

        except Exception as e:
            print(f"❌ Failed to connect: {e}")
            if database_url == database_urls[-1]:  # Last URL
                print("❌ Could not connect to any database")
                return False
            continue

    try:
        
        # Update admin role
        admin_permissions = [
            "invoices:read", "invoices:write", "invoices:delete",
            "users:read", "users:write", "users:delete",
            "accounting:read", "accounting:write", "accounting:validate",
            "settings:read", "settings:write",
            "reports:read", "reports:write",
            "action_items:read", "action_items:write", "action_items:assign"
        ]
        
        session.execute(
            text("UPDATE roles SET permissions = :permissions WHERE name = 'admin'"),
            {"permissions": json.dumps(admin_permissions)}
        )
        print("✓ Updated admin role permissions")
        
        # Update manager role
        manager_permissions = [
            "invoices:read", "invoices:write",
            "users:read", "users:write",
            "accounting:read", "accounting:validate",
            "reports:read", "reports:write",
            "action_items:read", "action_items:write", "action_items:assign"
        ]
        
        session.execute(
            text("UPDATE roles SET permissions = :permissions WHERE name = 'manager'"),
            {"permissions": json.dumps(manager_permissions)}
        )
        print("✓ Updated manager role permissions")
        
        # Update accountant role
        accountant_permissions = [
            "invoices:read", "invoices:write",
            "accounting:read", "accounting:write", "accounting:validate",
            "reports:read",
            "action_items:read", "action_items:write"
        ]
        
        session.execute(
            text("UPDATE roles SET permissions = :permissions WHERE name = 'accountant'"),
            {"permissions": json.dumps(accountant_permissions)}
        )
        print("✓ Updated accountant role permissions")
        
        # Update viewer role
        viewer_permissions = [
            "invoices:read",
            "accounting:read",
            "reports:read",
            "action_items:read"
        ]
        
        session.execute(
            text("UPDATE roles SET permissions = :permissions WHERE name = 'viewer'"),
            {"permissions": json.dumps(viewer_permissions)}
        )
        print("✓ Updated viewer role permissions")
        
        # Commit changes
        session.commit()
        print("\n✅ All role permissions updated successfully!")
        
        # Verify the changes
        print("\nVerifying admin role permissions:")
        result = session.execute(text("SELECT permissions FROM roles WHERE name = 'admin'")).fetchone()
        if result:
            permissions = result[0]
            print(f"Admin permissions: {permissions}")
            if 'action_items:read' in permissions:
                print("✅ action_items:read permission found!")
            else:
                print("❌ action_items:read permission missing!")
        
        session.close()
        
    except Exception as e:
        print(f"❌ Error updating permissions: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = update_role_permissions()
    sys.exit(0 if success else 1)
